/* Modal do perfil */
.modalOverlay {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(5px);
  display: flex;
  justify-content: center;
  align-items: flex-start;
  z-index: 1000;
  padding: 1rem;
  overflow-y: auto;
  /* Garantir posicionamento correto */
  transform: none !important;
  margin: 0 !important;
}

.profileModal {
  background-color: #23272f;
  border-radius: 1rem;
  width: 100%;
  max-width: 900px;
  max-height: calc(100vh - 2rem);
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  border: none;
  margin: 1rem auto;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  animation: slideIn 0.3s ease;
  /* Scroll customizado */
  scrollbar-width: thin;
  scrollbar-color: rgba(29, 185, 84, 0.5) rgba(255, 255, 255, 0.1);
}

@keyframes slideIn {
  from { transform: translateY(-20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

/* SCROLLBAR AGORA APENAS NO CONTEÚDO DO PERFIL */

.profileHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 1.5rem 1rem 1.5rem;
  border-bottom: 1px solid rgba(29, 185, 84, 0.2);
  flex-shrink: 0;
}

.profileHeader h2 {
  color: #1DB954;
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.profileHeader h2::before {
  content: '👤';
  font-size: 1.25rem;
}

/* Botão de fechar estilizado */
.closeButton {
  position: absolute;
  top: 1.5rem;
  right: 1.5rem;
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: #fff;
  font-size: 1.5rem;
  font-weight: bold;
  cursor: pointer;
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
  z-index: 1000;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  line-height: 1;
  padding: 0;
  text-align: center;
}

.closeButton:hover {
  color: #ff4444;
  background-color: rgba(255, 68, 68, 0.2);
  border-color: rgba(255, 68, 68, 0.5);
  transform: scale(1.1);
  box-shadow: 0 4px 15px rgba(255, 68, 68, 0.3);
}

.profileContent {
  padding: 1.5rem;
  overflow-y: auto !important;
  flex: 1;
  color: #e0e0e0;
  /* Scrollbar customizada verde */
  scrollbar-width: thin;
  scrollbar-color: rgba(29, 185, 84, 0.5) rgba(255, 255, 255, 0.1);
}

/* Webkit scrollbar para o conteúdo do perfil */
.profileContent::-webkit-scrollbar {
  width: 8px;
  background: transparent;
}

.profileContent::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.profileContent::-webkit-scrollbar-thumb {
  background: rgba(29, 185, 84, 0.5);
  border-radius: 4px;
  transition: background 0.3s ease;
}

.profileContent::-webkit-scrollbar-thumb:hover {
  background: rgba(29, 185, 84, 0.7);
}

/* Informações básicas */
.profileBasicInfo {
  display: flex;
  gap: 30px;
  margin-bottom: 30px;
  align-items: flex-start;
}

.avatarSection {
  position: relative;
  flex-shrink: 0;
  margin-bottom: 40px; /* Aumentado ainda mais para dar espaço ao badge */
  padding-bottom: 20px; /* Padding extra aumentado para o badge */
}

.avatar {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  border: 4px solid #1db954;
  object-fit: cover;
}

.levelBadge {
  position: absolute;
  bottom: -15px; /* Movido mais para baixo para não ser cortado */
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(45deg, #1db954, #1ed760);
  color: white;
  padding: 8px 18px; /* Padding aumentado para melhor visual */
  border-radius: 25px; /* Mais arredondado */
  font-size: 0.85rem;
  font-weight: 600;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4); /* Sombra mais forte */
  z-index: 2; /* Z-index maior */
  white-space: nowrap; /* Evita quebra de linha */
  border: 2px solid rgba(255, 255, 255, 0.1); /* Borda sutil */
}

.userInfo {
  flex: 1;
}

.userInfo h3 {
  margin: 0 0 10px 0;
  font-size: 2rem;
  display: flex;
  align-items: center;
  gap: 15px;
}

.editButton {
  background: none;
  border: none;
  color: #1db954;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.2s;
  font-size: 1rem;
}

.editButton:hover {
  background: rgba(29, 185, 84, 0.2);
  transform: scale(1.1);
}

.bio {
  color: #b3b3b3;
  margin: 10px 0;
  line-height: 1.5;
}

.joinDate {
  color: #888;
  font-size: 0.9rem;
}

/* Formulário de edição */
.editForm {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.editInput, .editTextarea {
  background-color: #181c22;
  border: 2px solid #23272f;
  border-radius: 0.7rem;
  color: #fff;
  padding: 0.75rem;
  font-size: 1rem;
  transition: border-color 0.2s, box-shadow 0.2s;
  box-sizing: border-box;
  font-family: inherit;
  resize: vertical;
}

.editInput:focus, .editTextarea:focus {
  outline: none;
  border-color: #1DB954;
  box-shadow: 0 0 0 2px rgba(29, 185, 84, 0.2);
}

.editButtons {
  display: flex;
  gap: 0.75rem;
}

.saveButton, .cancelButton {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 0.5rem;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.saveButton {
  background-color: #1DB954;
  color: white;
}

.saveButton:hover {
  background-color: #1ed760;
  transform: scale(1.02);
  box-shadow: 0 4px 15px rgba(29, 185, 84, 0.3);
}

.cancelButton {
  background: transparent;
  color: #b0b0b0;
  border: 2px solid #404040;
}

.cancelButton:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
}

/* Barra de XP */
.xpSection {
  margin-bottom: 2rem;
  background: #181c22;
  padding: 1.5rem;
  border-radius: 0.7rem;
  border: 2px solid #23272f;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.xpSection:hover {
  border-color: #1DB954;
  box-shadow: 0 0 0 2px rgba(29, 185, 84, 0.2);
}

.xpInfo {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.75rem;
  font-size: 0.9rem;
  color: #b0b0b0;
}

.xpBar {
  background: #23272f;
  height: 12px;
  border-radius: 6px;
  overflow: hidden;
  border: 1px solid rgba(29, 185, 84, 0.3);
}

.xpProgress {
  background: linear-gradient(90deg, #1db954, #1ed760);
  height: 100%;
  transition: width 0.3s ease;
  border-radius: 6px;
}

/* Navegação das abas */
.tabNavigation {
  display: flex;
  gap: 0.25rem;
  margin-bottom: 2rem;
  background: #181c22;
  padding: 0.25rem;
  border-radius: 0.7rem;
  border: 2px solid #23272f;
}

.tab {
  flex: 1;
  background: none;
  border: none;
  color: #b0b0b0;
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-weight: 600;
  font-size: 0.9rem;
}

.tab:hover {
  background: rgba(29, 185, 84, 0.1);
  color: #1DB954;
}

.tab.active {
  background: #1DB954;
  color: white;
  box-shadow: 0 2px 8px rgba(29, 185, 84, 0.3);
}

/* Conteúdo das abas */
.tabContent {
  min-height: 300px;
}

/* Aba de visão geral */
.statsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.statCard {
  background: #181c22;
  padding: 1.5rem;
  border-radius: 0.7rem;
  display: flex;
  align-items: center;
  gap: 1.25rem;
  border: 2px solid #23272f;
  transition: all 0.3s ease;
}

.statCard:hover {
  transform: translateY(-2px);
  border-color: #1DB954;
  box-shadow: 0 4px 15px rgba(29, 185, 84, 0.2);
}

.statIcon {
  font-size: 2rem;
  color: #1DB954;
  flex-shrink: 0;
}

.statInfo {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.statValue {
  font-size: 1.8rem;
  font-weight: 700;
  color: #fff;
  margin-bottom: 0.25rem;
}

.statLabel {
  font-size: 0.9rem;
  color: #b0b0b0;
}

/* Estatísticas por modo */
.modeStats {
  margin-bottom: 30px;
}

.modeStats h4 {
  margin-bottom: 20px;
  color: #1db954;
  font-size: 1.3rem;
}

.modeCard {
  background: #181c22;
  padding: 1.25rem;
  border-radius: 0.7rem;
  margin-bottom: 1rem;
  border: 2px solid #23272f;
  transition: all 0.3s ease;
}

.modeCard:hover {
  border-color: #1DB954;
  box-shadow: 0 0 0 2px rgba(29, 185, 84, 0.2);
}

.modeCard h5 {
  margin: 0 0 1rem 0;
  color: #1DB954;
  font-size: 1.1rem;
  font-weight: 600;
}

.modeInfo {
  display: flex;
  gap: 2rem;
  flex-wrap: wrap;
  color: #b0b0b0;
  font-size: 0.9rem;
}

/* Estatísticas de franquias */
.franchiseStats h4 {
  margin-bottom: 20px;
  color: #1db954;
  font-size: 1.3rem;
}

.franchiseList {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.franchiseItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #181c22;
  padding: 1rem 1.25rem;
  border-radius: 0.7rem;
  border: 2px solid #23272f;
  transition: all 0.3s ease;
}

.franchiseItem:hover {
  border-color: #1DB954;
  box-shadow: 0 0 0 2px rgba(29, 185, 84, 0.2);
}

.franchiseName {
  font-weight: 600;
  color: #fff;
}

.franchiseWinRate {
  color: #1DB954;
  font-weight: 600;
}

/* Aba de conquistas */
.achievementsSummary {
  margin-bottom: 30px;
  text-align: center;
}

.achievementsSummary h4 {
  color: #1db954;
  font-size: 1.3rem;
}

.nearAchievements {
  margin-bottom: 30px;
}

.nearAchievements h5 {
  color: #1db954;
  margin-bottom: 20px;
  font-size: 1.2rem;
}

.achievementItem {
  display: flex;
  align-items: center;
  gap: 1.25rem;
  background: #181c22;
  padding: 1.25rem;
  border-radius: 0.7rem;
  margin-bottom: 1rem;
  border: 2px solid #23272f;
  transition: all 0.3s ease;
}

.achievementItem:hover {
  border-color: #1DB954;
  box-shadow: 0 0 0 2px rgba(29, 185, 84, 0.2);
}

.achievementIcon {
  font-size: 2rem;
  flex-shrink: 0;
  color: #1DB954;
}

.achievementInfo {
  flex: 1;
}

.achievementTitle {
  display: block;
  font-weight: 600;
  color: #fff;
  margin-bottom: 0.25rem;
}

.achievementDesc {
  display: block;
  color: #b0b0b0;
  font-size: 0.9rem;
  margin-bottom: 0.75rem;
}

.achievementProgress {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.progressBar {
  flex: 1;
  height: 8px;
  background: #23272f;
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid rgba(29, 185, 84, 0.3);
}

.progressBar::before {
  content: '';
  display: block;
  height: 100%;
  background: linear-gradient(90deg, #1db954, #1ed760);
  border-radius: 4px;
}

.unlockedAchievements h5 {
  color: #1db954;
  margin-bottom: 20px;
  font-size: 1.2rem;
}

.achievementGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
}

.achievementCard {
  background: rgba(255, 255, 255, 0.05);
  padding: 20px;
  border-radius: 12px;
  text-align: center;
  border: 2px solid;
  transition: transform 0.2s;
}

.achievementCard:hover {
  transform: translateY(-5px);
}

.achievementCard .achievementIcon {
  font-size: 2.5rem;
  margin-bottom: 10px;
  display: block;
}

.achievementCard .achievementTitle {
  font-size: 1.1rem;
  font-weight: 600;
  color: white;
  margin-bottom: 8px;
}

.achievementCard .achievementDesc {
  font-size: 0.9rem;
  color: #b3b3b3;
  margin-bottom: 10px;
}

.achievementRarity {
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Aba de histórico */
.gameHistory {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.gameItem {
  background: rgba(255, 255, 255, 0.05);
  padding: 20px;
  border-radius: 12px;
  border: 1px solid rgba(29, 185, 84, 0.2);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
}

.gameResult {
  display: flex;
  align-items: center;
  gap: 15px;
}

.resultIcon {
  font-size: 1.5rem;
}

.resultIcon.win {
  color: #1db954;
}

.resultIcon.loss {
  color: #e22134;
}

.gameInfo {
  display: flex;
  flex-direction: column;
}

.gameMode {
  font-weight: 600;
  color: white;
}

.gameDate {
  font-size: 0.9rem;
  color: #b3b3b3;
}

.songInfo {
  display: flex;
  flex-direction: column;
  text-align: center;
}

.songTitle {
  font-weight: 600;
  color: white;
}

.songGame {
  font-size: 0.9rem;
  color: #b3b3b3;
}

.gameStats {
  display: flex;
  flex-direction: column;
  text-align: right;
  font-size: 0.9rem;
  color: #b3b3b3;
}

.noHistory {
  text-align: center;
  color: #888;
  font-style: italic;
  padding: 40px;
}

/* Responsividade */
@media (max-width: 768px) {
  .profileModal {
    margin: 10px;
    max-height: 95vh;
  }

  .profileContent {
    padding: 20px;
  }
  
  .profileBasicInfo {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }
  
  .statsGrid {
    grid-template-columns: 1fr;
  }
  
  .modeInfo {
    flex-direction: column;
    gap: 10px;
  }
  
  .achievementGrid {
    grid-template-columns: 1fr;
  }
  
  .gameItem {
    flex-direction: column;
    text-align: center;
  }
  
  .tab {
    padding: 12px 8px;
    font-size: 0.9rem;
  }

  .dataActions {
    flex-direction: column;
  }

  .confirmButtons {
    flex-direction: column;
  }
}

/* Aba de configurações */
.settingsTab h4 {
  color: #1db954;
  margin-bottom: 30px;
  font-size: 1.3rem;
}

.settingsSection {
  background: rgba(255, 255, 255, 0.05);
  padding: 25px;
  border-radius: 15px;
  margin-bottom: 25px;
  border: 1px solid rgba(29, 185, 84, 0.2);
}

.settingsSection h5 {
  color: white;
  margin-bottom: 20px;
  font-size: 1.1rem;
  font-weight: 600;
}

.settingItem {
  margin-bottom: 15px;
}

.settingItem label {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #b3b3b3;
  cursor: pointer;
  font-size: 1rem;
}

.settingItem input[type="checkbox"] {
  width: 18px;
  height: 18px;
  accent-color: #1db954;
}

.dataActions {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.exportButton,
.importButton {
  background: #1db954;
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 10px;
  cursor: pointer;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s;
  text-decoration: none;
}

.exportButton:hover,
.importButton:hover {
  background: #1ed760;
  transform: translateY(-2px);
}

.profileStats {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.statItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.statItem:last-child {
  border-bottom: none;
}

.statItem span:first-child {
  color: #b3b3b3;
}

.statItem span:last-child {
  color: white;
  font-weight: 600;
}

.dangerZone {
  color: #ef4444 !important;
}

.resetButton {
  background: #ef4444;
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 10px;
  cursor: pointer;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s;
}

.resetButton:hover {
  background: #dc2626;
  transform: translateY(-2px);
}

.confirmReset {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid #ef4444;
  padding: 20px;
  border-radius: 10px;
}

.confirmReset p {
  color: #ef4444;
  margin-bottom: 15px;
  font-weight: 600;
}

.confirmButtons {
  display: flex;
  gap: 10px;
}

.confirmResetButton {
  background: #ef4444;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.2s;
}

.confirmResetButton:hover {
  background: #dc2626;
}

.cancelResetButton {
  background: transparent;
  color: #888;
  border: 1px solid #888;
  padding: 10px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.2s;
}

.cancelResetButton:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

/* Botão Deletar Conta */
.deleteButton {
  background: #7c2d12; /* Marrom escuro mais perigoso */
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 10px;
  cursor: pointer;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s;
  margin-top: 15px;
}

.deleteButton:hover {
  background: #991b1b; /* Vermelho escuro */
  transform: translateY(-2px);
}

.confirmDelete {
  background: rgba(124, 45, 18, 0.1);
  border: 1px solid #7c2d12;
  padding: 20px;
  border-radius: 10px;
  margin-top: 15px;
}

.confirmDelete p {
  color: #dc2626;
  margin-bottom: 10px;
  font-weight: 600;
}

.confirmDeleteButton {
  background: #7c2d12;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.2s;
}

.confirmDeleteButton:hover {
  background: #991b1b;
}

.cancelDeleteButton {
  background: transparent;
  color: #888;
  border: 1px solid #888;
  padding: 10px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.2s;
}

.cancelDeleteButton:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

/* Aba de badges */
.badgesTab h4 {
  color: #1db954;
  margin-bottom: 30px;
  font-size: 1.3rem;
}

.currentTitleSection {
  background: rgba(255, 255, 255, 0.05);
  padding: 20px;
  border-radius: 15px;
  margin-bottom: 25px;
  border: 1px solid rgba(29, 185, 84, 0.2);
  text-align: center;
}

.currentTitleSection h5 {
  color: white;
  margin-bottom: 15px;
  font-size: 1.1rem;
}

.currentTitle {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  background: linear-gradient(45deg, #1db954, #1ed760);
  padding: 15px 25px;
  border-radius: 25px;
  color: white;
  font-weight: 600;
  font-size: 1.1rem;
}

.titleIcon {
  font-size: 1.5rem;
}

.titlesSection {
  background: rgba(255, 255, 255, 0.05);
  padding: 20px;
  border-radius: 15px;
  margin-bottom: 25px;
  border: 1px solid rgba(29, 185, 84, 0.2);
}

.titlesSection h5 {
  color: white;
  margin-bottom: 15px;
  font-size: 1.1rem;
}

.titlesGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 10px;
}

.titleOption {
  background: rgba(255, 255, 255, 0.1);
  padding: 12px 15px;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.2s;
  border: 2px solid transparent;
  text-align: center;
}

.titleOption:hover {
  background: rgba(29, 185, 84, 0.2);
  border-color: rgba(29, 185, 84, 0.5);
}

.titleOption.selected {
  background: rgba(29, 185, 84, 0.3);
  border-color: #1db954;
}

.titleOptionText {
  color: white;
  font-weight: 600;
  font-size: 0.9rem;
}

.badgesSection,
.nextBadgesSection {
  background: rgba(255, 255, 255, 0.05);
  padding: 20px;
  border-radius: 15px;
  margin-bottom: 25px;
  border: 1px solid rgba(29, 185, 84, 0.2);
}

.badgesSection h5,
.nextBadgesSection h5 {
  color: white;
  margin-bottom: 15px;
  font-size: 1.1rem;
}

.badgesGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 15px;
}

.badgeItem {
  background: rgba(255, 255, 255, 0.1);
  padding: 15px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  gap: 15px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.2s;
}

.badgeItem:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
}

.badgeIcon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  font-weight: bold;
}

.badgeInfo {
  flex: 1;
}

.badgeTitle {
  color: white;
  font-weight: 600;
  margin-bottom: 5px;
  font-size: 1rem;
}

.badgeDescription {
  color: #b3b3b3;
  font-size: 0.9rem;
  margin-bottom: 8px;
  line-height: 1.3;
}

.badgeRarity {
  font-size: 0.8rem;
  font-weight: 600;
  padding: 3px 8px;
  border-radius: 12px;
  display: inline-block;
}

.badgeRarity.common {
  background: rgba(156, 163, 175, 0.3);
  color: #9CA3AF;
}

.badgeRarity.uncommon {
  background: rgba(34, 197, 94, 0.3);
  color: #22C55E;
}

.badgeRarity.rare {
  background: rgba(59, 130, 246, 0.3);
  color: #3B82F6;
}

.badgeRarity.epic {
  background: rgba(168, 85, 247, 0.3);
  color: #A855F7;
}

.badgeRarity.legendary {
  background: rgba(245, 158, 11, 0.3);
  color: #F59E0B;
}

.noBadges {
  color: #888;
  text-align: center;
  padding: 40px 20px;
  font-style: italic;
}

.nextBadgesGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 12px;
}

.nextBadgeItem {
  background: rgba(255, 255, 255, 0.05);
  padding: 12px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 12px;
  border: 1px solid rgba(255, 255, 255, 0.05);
  opacity: 0.7;
}

.nextBadgeIcon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  color: white;
  opacity: 0.6;
}

.nextBadgeInfo {
  flex: 1;
}

.nextBadgeTitle {
  color: #ccc;
  font-weight: 600;
  margin-bottom: 3px;
  font-size: 0.9rem;
}

.nextBadgeDescription {
  color: #888;
  font-size: 0.8rem;
  line-height: 1.2;
}

/* Responsividade melhorada */
@media (max-width: 768px) {
  .modalOverlay {
    padding: 5px;
    align-items: flex-start;
  }

  .profileModal {
    max-width: 100%;
    max-height: calc(100vh - 80px);
    margin: 40px auto 40px auto;
    border-radius: 15px;
  }

  .profileContent {
    padding: 15px;
  }

  .profileBasicInfo {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 15px;
    padding: 15px;
  }

  .tabNavigation {
    flex-wrap: wrap;
    gap: 5px;
    padding: 10px;
  }

  .tab {
    flex: 1;
    min-width: 100px;
    padding: 10px 8px;
    font-size: 0.8rem;
  }

  .tabContent {
    padding: 15px;
  }

  .statsGrid {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .achievementGrid {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .badgesGrid {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .titlesGrid {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .nextBadgesGrid {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .modeInfo {
    flex-direction: column;
    gap: 10px;
  }

  .gameItem {
    flex-direction: column;
    text-align: center;
    padding: 10px;
  }

  .dataActions {
    flex-direction: column;
    gap: 10px;
  }

  .confirmButtons {
    flex-direction: column;
    gap: 10px;
  }

  .settingsSection,
  .badgesSection,
  .nextBadgesSection,
  .titlesSection,
  .currentTitleSection {
    padding: 15px;
    margin-bottom: 15px;
  }

  .badgeItem {
    flex-direction: column;
    text-align: center;
    gap: 10px;
  }

  .nextBadgeItem {
    flex-direction: column;
    text-align: center;
    gap: 8px;
  }

  .profileHeader h3 {
    font-size: 1.3rem;
  }

  .levelBadge {
    font-size: 0.9rem;
    padding: 6px 12px;
  }

  .xpProgress {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .profileModal {
    max-height: calc(100vh - 40px);
    margin: 20px auto 20px auto;
    border-radius: 10px;
  }

  .profileContent {
    padding: 10px;
  }

  .tabNavigation {
    padding: 5px;
  }

  .tab {
    min-width: 80px;
    padding: 8px 6px;
    font-size: 0.7rem;
  }

  .tabContent {
    padding: 10px;
  }

  .settingsSection,
  .badgesSection,
  .nextBadgesSection,
  .titlesSection,
  .currentTitleSection {
    padding: 10px;
    margin-bottom: 10px;
  }

  .profileHeader h3 {
    font-size: 1.1rem;
  }

  .statCard h4 {
    font-size: 0.9rem;
  }

  .statCard .value {
    font-size: 1.3rem;
  }
}

/* Seção de benefícios de doação */
.benefitsSection {
  background: rgba(255, 255, 255, 0.05);
  padding: 20px;
  border-radius: 12px;
  border: 1px solid rgba(29, 185, 84, 0.2);
}

.benefitsSection p {
  color: #b3b3b3;
  margin-bottom: 20px;
  line-height: 1.5;
}

.activateBenefitsButton {
  background: linear-gradient(45deg, #ff6b6b, #ffd700);
  color: white;
  border: none;
  padding: 15px 25px;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 20px;
  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

.activateBenefitsButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
  background: linear-gradient(45deg, #ff5252, #ffeb3b);
}

.currentBenefits {
  background: rgba(255, 255, 255, 0.03);
  padding: 15px;
  border-radius: 10px;
  border: 1px solid rgba(29, 185, 84, 0.1);
}

.currentBenefits h6 {
  color: #1db954;
  margin: 0 0 15px 0;
  font-size: 1rem;
  font-weight: 600;
}

.benefitsList {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.benefit {
  background: linear-gradient(45deg, #1db954, #1ed760);
  color: white;
  padding: 8px 12px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 5px;
  box-shadow: 0 2px 8px rgba(29, 185, 84, 0.3);
}

/* Melhorias finais para consistência com o site */
.benefitsSection {
  background: #181c22 !important;
  border: 2px solid #23272f !important;
  transition: all 0.3s ease;
}

.benefitsSection:hover {
  border-color: #1DB954 !important;
  box-shadow: 0 0 0 2px rgba(29, 185, 84, 0.2);
}

.settingsSection,
.badgesSection,
.nextBadgesSection,
.titlesSection,
.currentTitleSection {
  background: #181c22 !important;
  border: 2px solid #23272f !important;
  transition: all 0.3s ease;
}

.settingsSection:hover,
.badgesSection:hover,
.nextBadgesSection:hover,
.titlesSection:hover,
.currentTitleSection:hover {
  border-color: #1DB954 !important;
  box-shadow: 0 0 0 2px rgba(29, 185, 84, 0.2);
}

/* Scrollbar customizada para modal */
.profileModal::-webkit-scrollbar {
  width: 8px;
}

.profileModal::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.profileModal::-webkit-scrollbar-thumb {
  background: rgba(29, 185, 84, 0.5);
  border-radius: 4px;
  transition: background 0.3s ease;
}

.profileModal::-webkit-scrollbar-thumb:hover {
  background: rgba(29, 185, 84, 0.7);
}
