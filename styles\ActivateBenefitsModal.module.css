/* Modal de ativação de benefícios */
.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  padding: 1rem;
  overflow-y: auto;
}

.modal {
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  border-radius: 20px;
  max-width: 500px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 25px 80px rgba(0, 0, 0, 0.8);
  border: 2px solid #3b82f6;
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem 2rem 1rem 2rem;
  border-bottom: 1px solid #333;
}

.modalHeader h2 {
  color: #fff;
  margin: 0;
  font-size: 1.8rem;
  background: linear-gradient(45deg, #3b82f6, #1d4ed8);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.closeButton {
  background: none;
  border: none;
  color: #999;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.closeButton:hover {
  background: #333;
  color: #fff;
}

.modalContent {
  padding: 2rem;
}

.description {
  margin-bottom: 2rem;
  text-align: center;
}

.description p {
  color: #ccc;
  font-size: 1rem;
  line-height: 1.5;
  margin: 0;
}

.codeInput {
  margin-bottom: 1.5rem;
}

.codeInput label {
  display: block;
  color: #fff;
  font-weight: bold;
  margin-bottom: 0.5rem;
  font-size: 1rem;
}

.input {
  width: 100%;
  padding: 1rem;
  background: #1a1a1a;
  border: 2px solid #333;
  border-radius: 10px;
  color: #fff;
  font-size: 1.2rem;
  font-family: monospace;
  text-align: center;
  letter-spacing: 2px;
  transition: all 0.3s ease;
}

.input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.input::placeholder {
  color: #666;
  letter-spacing: 1px;
}

.error {
  background: #dc2626;
  color: white;
  padding: 1rem;
  border-radius: 10px;
  margin-bottom: 1.5rem;
  text-align: center;
  font-weight: bold;
}

.actions {
  text-align: center;
  margin-bottom: 2rem;
}

.activateButton {
  background: linear-gradient(45deg, #3b82f6, #1d4ed8);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 10px;
  font-weight: bold;
  font-size: 1.1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 200px;
}

.activateButton:hover:not(:disabled) {
  background: linear-gradient(45deg, #1d4ed8, #1e40af);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(59, 130, 246, 0.3);
}

.activateButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.help {
  background: #2a2a2a;
  padding: 1.5rem;
  border-radius: 10px;
  border-left: 4px solid #3b82f6;
}

.help h4 {
  color: #fff;
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
}

.help ul {
  color: #ccc;
  margin: 0;
  padding-left: 1.5rem;
}

.help li {
  margin: 0.5rem 0;
  line-height: 1.4;
}

/* Responsividade */
@media (max-width: 768px) {
  .modal {
    margin: 1rem;
    max-width: calc(100% - 2rem);
  }

  .modalHeader {
    padding: 1.5rem;
  }

  .modalHeader h2 {
    font-size: 1.5rem;
  }

  .modalContent {
    padding: 1.5rem;
  }

  .input {
    font-size: 1rem;
    letter-spacing: 1px;
  }

  .activateButton {
    width: 100%;
    min-width: auto;
  }
}

@media (max-width: 480px) {
  .modalHeader {
    padding: 1rem;
  }

  .modalContent {
    padding: 1rem;
  }

  .help {
    padding: 1rem;
  }

  .input {
    font-size: 0.9rem;
    padding: 0.8rem;
  }
}
