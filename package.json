{"name": "ludomusic", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "export": "set BUILD_STATIC=true && next build", "build:static": "set BUILD_STATIC=true && next build", "build:hostinger": "npm run export && node scripts/build-for-hostinger.js", "prepare:deploy": "npm run build:hostinger", "generate:sitemap": "node scripts/generate-sitemap.js", "seo:optimize": "npm run generate:sitemap", "upload:r2": "node scripts/upload-to-r2.js", "update:urls": "node scripts/update-audio-urls.js", "migrate:r2": "npm run upload:r2 && npm run update:urls"}, "dependencies": {"@aws-sdk/client-s3": "^3.823.0", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@vercel/analytics": "^1.5.0", "@vercel/kv": "^3.0.0", "@vercel/speed-insights": "^1.2.0", "bcryptjs": "^3.0.2", "crypto": "^1.0.1", "dotenv": "^16.5.0", "formidable": "^3.5.4", "next": "^14.1.0", "node-fetch": "^3.3.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^5.5.0", "resend": "^4.5.1"}}