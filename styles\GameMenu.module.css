.menuOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: flex-start;
  z-index: 1000;
  padding: 80px 20px 20px 20px; /* Mais espaço do topo para ficar na linha vermelha */
  overflow-y: auto;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.menuContainer {
  background-color: #23272f;
  border-radius: 1rem;
  width: 90%;
  max-width: 500px;
  max-height: calc(100vh - 120px);
  overflow-y: auto;
  padding: 1.5rem;
  position: relative;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  margin: 60px auto 60px auto;
  animation: slideIn 0.3s ease;
  /* Scroll customizado */
  scrollbar-width: thin;
  scrollbar-color: rgba(29, 185, 84, 0.5) rgba(255, 255, 255, 0.1);
}

/* Scrollbar customizada para Webkit */
.menuContainer::-webkit-scrollbar {
  width: 8px;
}

.menuContainer::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.menuContainer::-webkit-scrollbar-thumb {
  background: rgba(29, 185, 84, 0.5);
  border-radius: 4px;
  transition: background 0.3s ease;
}

.menuContainer::-webkit-scrollbar-thumb:hover {
  background: rgba(29, 185, 84, 0.7);
}

@keyframes slideIn {
  from { transform: translateY(-20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.closeButton {
  position: absolute;
  top: 1.5rem;
  right: 1.5rem;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #fff;
  font-size: 1.5rem;
  cursor: pointer;
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
  z-index: 10;
}

.closeButton:hover {
  color: #1DB954;
  background-color: rgba(29, 185, 84, 0.1);
}

.menuSection {
  margin-bottom: 0.8rem;
  border-radius: 0.7rem;
  overflow: hidden;
}

.menuSectionHeader {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 1rem;
  background-color: #181c22;
  border: none;
  color: #1DB954;
  font-size: 1rem;
  font-weight: 600;
  text-align: center;
  cursor: pointer;
  transition: background-color 0.2s, color 0.2s;
  border-radius: 0.7rem;
  position: relative;
}

.menuSectionHeader:hover {
  background-color: #1a1e24;
  color: #1ed760;
}

.menuIcon {
  position: absolute;
  left: 1rem;
  width: 1.2rem;
}

.expandIcon {
  position: absolute;
  right: 1rem;
  font-size: 0.8rem;
  color: #1DB954;
}

.menuSectionContent {
  background-color: #1a1e24;
  padding: 1rem;
  color: #e0e0e0;
  font-size: 0.9rem;
  line-height: 1.5;
  animation: expandContent 0.3s ease;
  border-top: 1px solid rgba(29, 185, 84, 0.2);
}

@keyframes expandContent {
  from { opacity: 0; max-height: 0; }
  to { opacity: 1; max-height: 500px; }
}

.menuSectionContent p {
  margin: 0.5rem 0;
}

.settingItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0.75rem 0;
}

.toggleSwitch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
  cursor: pointer;
  user-select: none;
}

.toggleSwitch input {
  opacity: 0;
  width: 0;
  height: 0;
  position: absolute;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #181c22;
  transition: .3s;
  border-radius: 24px;
  border: 1px solid #333;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.3);
}

.slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 4px;
  bottom: 3px;
  background-color: white;
  transition: .3s;
  border-radius: 50%;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.toggleSwitch:active .slider:before {
  width: 20px;
}

input:checked + .slider {
  background-color: #1DB954;
}

input:checked + .slider:before {
  transform: translateX(26px);
}

.toggleSwitch:hover .slider {
  border-color: #1DB954;
}

.selectInput {
  background-color: #181c22;
  color: #fff;
  border: 1px solid #333;
  padding: 0.5rem;
  border-radius: 0.5rem;
  width: 180px;
  cursor: pointer;
  font-size: 0.9rem;
}

.selectInput:focus {
  outline: none;
  border-color: #1DB954;
  box-shadow: 0 0 0 2px rgba(29, 185, 84, 0.2);
}

.menuLink {
  color: #1DB954;
  text-decoration: none;
  display: inline-block;
  margin-top: 0.5rem;
  transition: color 0.2s;
}

.menuLink:hover {
  color: #1ed760;
  text-decoration: underline;
}

.actionButton, .submitButton {
  background-color: #1DB954;
  color: #fff;
  border: none;
  padding: 0.6rem 1rem;
  border-radius: 0.5rem;
  cursor: pointer;
  margin-top: 0.8rem;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.4rem;
  font-weight: 500;
  font-size: 0.9rem;
  max-width: 200px;
  align-self: center;
}

.actionButton:hover, .submitButton:hover {
  background-color: #1ed760;
  transform: scale(1.02);
}

.actionButton:disabled, .submitButton:disabled {
  background-color: #444;
  color: #aaa;
  cursor: not-allowed;
  transform: none;
}

.errorReportForm {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 1rem;
}

.formGroup {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.formGroup label {
  font-weight: 600;
  color: #e0e0e0;
  font-size: 0.95rem;
}

.textArea, .textInput {
  background-color: #181c22;
  border: 2px solid #23272f;
  border-radius: 0.7rem;
  padding: 0.75rem;
  color: #fff;
  font-family: inherit;
  font-size: 0.95rem;
  resize: vertical;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.textArea:focus, .textInput:focus {
  outline: none;
  border-color: #1DB954;
  box-shadow: 0 0 0 2px rgba(29, 185, 84, 0.2);
}

.errorMessage {
  background-color: rgba(231, 76, 60, 0.1);
  border-left: 3px solid #e74c3c;
  padding: 0.75rem;
  color: #e74c3c;
  font-size: 0.9rem;
  margin-top: 0.5rem;
  border-radius: 0 0.5rem 0.5rem 0;
}

.successMessage {
  background-color: rgba(39, 174, 96, 0.1);
  border-left: 3px solid #27ae60;
  padding: 0.75rem;
  color: #27ae60;
  font-size: 0.9rem;
  margin-top: 0.5rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  border-radius: 0 0.5rem 0.5rem 0;
}

.submitIcon {
  margin-left: 0.3rem;
  font-size: 0.8rem;
}

/* Estilos para o botão de menu na barra superior */
.menuButton {
  background: none;
  border: none;
  color: #fff;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.menuButton:hover {
  color: #1DB954;
  transform: scale(1.1);
}

/* Responsividade */
@media (max-width: 768px) {
  .menuContainer {
    width: 95%;
    max-height: calc(100vh - 80px);
    margin: 40px auto 40px auto;
    padding: 1rem;
  }

  .menuSectionHeader {
    padding: 0.75rem;
    font-size: 0.95rem;
  }

  .menuIcon {
    left: 0.75rem;
  }

  .expandIcon {
    right: 0.75rem;
  }

  .menuSectionContent {
    padding: 0.75rem;
    font-size: 0.85rem;
  }
}

@media (max-width: 480px) {
  .menuContainer {
    width: 100%;
    max-width: none;
    max-height: calc(100vh - 40px);
    margin: 20px auto 20px auto;
    border-radius: 0;
  }

  .menuSectionHeader {
    padding: 0.7rem;
    font-size: 0.9rem;
  }

  .menuIcon {
    left: 0.7rem;
  }

  .expandIcon {
    right: 0.7rem;
  }

  .menuSectionContent {
    padding: 0.7rem;
    font-size: 0.8rem;
  }

  .closeButton {
    top: 1rem;
    right: 1rem;
    width: 2rem;
    height: 2rem;
    font-size: 1.3rem;
  }
}
