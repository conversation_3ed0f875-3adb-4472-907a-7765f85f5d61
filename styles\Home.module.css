.container {
  min-height: 100vh;
  min-height: 100dvh; /* Para dispositivos móveis */
  width: 100%;
  padding: 2rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  color: white;
  position: relative;
  box-sizing: border-box;

  /* Suporte a safe areas para iOS */
  padding-top: max(2rem, env(safe-area-inset-top));
  padding-bottom: max(2rem, env(safe-area-inset-bottom));
  padding-left: max(2rem, env(safe-area-inset-left));
  padding-right: max(2rem, env(safe-area-inset-right));

  /* Variáveis CSS responsivas */
  --container-padding: 2rem;
  --font-scale: 1;
  --spacing-scale: 1;
  --animation-duration: 0.3s;
  --transition-duration: 0.3s;
  --touch-target-size: 44px;
}

/* 📱 RESPONSIVIDADE AVANÇADA */

/* Classes de otimização por dispositivo aplicadas via JavaScript */
.mobileOptimized {
  --animation-duration: 0.1s;
  --transition-duration: 0.1s;
  --font-scale: 0.9;
  --spacing-scale: 0.8;
}

.tabletOptimized {
  --animation-duration: 0.2s;
  --transition-duration: 0.2s;
  --font-scale: 1;
  --spacing-scale: 0.9;
}

.desktopOptimized {
  --animation-duration: 0.3s;
  --transition-duration: 0.3s;
  --font-scale: 1.1;
  --spacing-scale: 1.1;
}

.touchOptimized {
  --touch-target-size: 48px;
  --touch-spacing: 8px;
}

.noAnimations * {
  animation-duration: 0s !important;
  transition-duration: 0s !important;
}

.noShadows * {
  box-shadow: none !important;
  text-shadow: none !important;
}

/* Breakpoints específicos */
@media (max-width: 575px) {
  .container {
    --container-padding: 0.5rem;
    --font-scale: 0.85;
    --spacing-scale: 0.7;
    padding: var(--container-padding);
  }
}

@media (min-width: 576px) and (max-width: 767px) {
  .container {
    --container-padding: 1rem;
    --font-scale: 0.9;
    --spacing-scale: 0.8;
    padding: var(--container-padding);
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  .container {
    --container-padding: 1.5rem;
    --font-scale: 1;
    --spacing-scale: 0.9;
    padding: var(--container-padding);
  }
}

@media (min-width: 992px) and (max-width: 1199px) {
  .container {
    --container-padding: 2rem;
    --font-scale: 1.05;
    --spacing-scale: 1;
    padding: var(--container-padding);
  }
}

@media (min-width: 1200px) {
  .container {
    --container-padding: 2.5rem;
    --font-scale: 1.1;
    --spacing-scale: 1.1;
    padding: var(--container-padding);
  }
}

/* Orientação landscape em mobile - MELHORADA */
@media (max-height: 500px) and (orientation: landscape) {
  .container {
    min-height: 100vh;
    padding: 0.25rem;
    justify-content: flex-start;
    overflow-y: auto; /* Permitir scroll se necessário */
  }

  .gameAreaModern {
    margin: 0.5rem auto;
    padding: 0.75rem;
    max-width: 100%;
  }

  /* Compactar header em landscape */
  .titleBarContainer {
    padding: 0.25rem;
    min-height: 60px; /* Reduzir altura mínima */
  }

  .titleBarContainer img {
    height: 40px; /* Logo menor */
    max-width: 100px;
  }

  /* Compactar controles de áudio */
  .audioModernBox {
    padding: 0.75rem;
    margin-bottom: 0.75rem;
  }

  /* Compactar botões de tentativa */
  .attemptsRow {
    margin: 0.75rem 0;
  }

  .attemptButton {
    min-width: 1.8rem;
    min-height: 1.8rem;
    padding: 0.3rem 0.7rem;
    font-size: 1rem;
  }

  /* Compactar formulário */
  .guessFormModern {
    margin-top: 0.75rem;
  }

  /* Sugestões em landscape */
  .suggestionsListModern {
    max-height: 150px; /* Altura reduzida para landscape */
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .container {
    --border-width: 0.5px;
    --shadow-blur: 0.5px;
  }
}

/* Dispositivos com pouca altura */
@media (max-height: 600px) {
  .container {
    padding-top: 1rem;
    padding-bottom: 1rem;
  }

  .title {
    font-size: calc(2rem * var(--font-scale));
    margin-bottom: calc(1rem * var(--spacing-scale));
  }
}

.title {
  font-size: 4rem;
  margin-bottom: 2rem;
  color: #1DB954;
  text-shadow: 0 0 10px rgba(29, 185, 84, 0.5);
}

/* 📱 TÍTULO RESPONSIVO */
@media (max-width: 768px) {
  .title {
    font-size: 2.5rem;
    margin-bottom: 1rem;
  }
}

@media (max-width: 480px) {
  .title {
    font-size: 2rem;
    margin-bottom: 0.5rem;
  }
}

.gameArea {
  background: rgba(255, 255, 255, 0.1);
  padding: 2rem;
  border-radius: 1rem;
  width: 100%;
  max-width: 600px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5rem;
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

/* 📱 GAME AREA RESPONSIVO */
@media (max-width: 768px) {
  .gameArea {
    padding: 1.5rem;
    gap: 1rem;
    margin: 0 0.5rem;
  }
}

@media (max-width: 480px) {
  .gameArea {
    padding: 1rem;
    gap: 0.8rem;
    margin: 0 0.25rem;
  }
}

.playButton {
  background: #1DB954;
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 2rem;
  font-size: 1.2rem;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  max-width: 300px;
}

.playButton:hover {
  background: #1ed760;
  transform: scale(1.05);
}

.playButton:disabled {
  background: #666;
  cursor: not-allowed;
  transform: none;
}

.attempts {
  font-size: 1.2rem;
  color: #1DB954;
}

.audioControls {
  display: flex;
  align-items: center;
  gap: 1rem;
  width: 100%;
  justify-content: center;
  margin-bottom: 1rem;
}

.stopButton {
  background: #ff6b6b;
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 2rem;
  font-size: 1.2rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.stopButton:hover {
  background: #ff8787;
  transform: scale(1.05);
}

.stopButton:disabled {
  background: #666;
  cursor: not-allowed;
  transform: none;
}

.volumeSlider {
  width: 120px;
  accent-color: #1DB954;
  margin-left: 1rem;
}

.suggestionsList {
  position: absolute;
  background: #222;
  border: 1px solid #1DB954;
  border-radius: 0.5rem;
  margin-top: 0.5rem;
  width: 100%;
  max-height: 200px;
  overflow-y: auto;
  z-index: 10;
  list-style: none;
  padding: 0;
  box-shadow: 0 4px 16px rgba(0,0,0,0.3);
}

.suggestionItem {
  padding: 0.75rem 1rem;
  cursor: pointer;
  color: #fff;
  transition: background 0.2s;
}

.suggestionItem:hover {
  background: #1DB954;
  color: #222;
}

.guessForm {
  position: relative;
  width: 100%;
  display: flex;
  gap: 1rem;
}

.guessInput {
  flex: 1;
  padding: 1rem;
  border: 2px solid #1DB954;
  border-radius: 0.5rem;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 1rem;
}

.guessInput:focus {
  outline: none;
  border-color: #1ed760;
}

.guessButton {
  background: #1DB954;
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.guessButton:hover {
  background: #1ed760;
}

.guessButton:disabled {
  background: #666;
  cursor: not-allowed;
}

.message {
  font-size: 1.2rem;
  text-align: center;
  padding: 1rem;
  border-radius: 0.5rem;
  background: rgba(255, 255, 255, 0.1);
  width: 100%;
}

.message.error {
  background: rgba(255, 0, 0, 0.2);
  border: 1px solid rgba(255, 0, 0, 0.5);
  color: #ff6b6b;
}

.newGameButton {
  background: #1DB954;
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 2rem;
  font-size: 1.2rem;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 1rem;
}

.newGameButton:hover {
  background: #1ed760;
  transform: scale(1.05);
}

.instructions {
  margin-top: 2rem;
  text-align: center;
  max-width: 600px;
}

.instructions h2 {
  color: #1DB954;
  margin-bottom: 1rem;
}

.instructions p {
  margin: 0.5rem 0;
  color: #ccc;
}

/* ✨ LOADING STATES MELHORADOS */
.loading {
  font-size: 1.5rem;
  color: #1DB954;
  animation: loadingPulse 1.5s infinite;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.loading::before {
  content: '';
  width: 20px;
  height: 20px;
  border: 2px solid #1DB954;
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: loadingSpin 1s linear infinite;
}

/* Spinner para botões */
.buttonLoading {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.buttonLoading::before {
  content: '';
  width: 16px;
  height: 16px;
  border: 2px solid currentColor;
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: loadingSpin 1s linear infinite;
}

/* Loading para áudio */
.audioLoading {
  background: rgba(29, 185, 84, 0.1);
  border: 2px dashed #1DB954;
  border-radius: 12px;
  padding: 2rem;
  text-align: center;
  color: #1DB954;
  font-size: 1.1rem;
  animation: loadingPulse 2s infinite;
}

.audioLoading::before {
  content: '🎵';
  display: block;
  font-size: 2rem;
  margin-bottom: 0.5rem;
  animation: loadingBounce 1.5s infinite;
}

@keyframes loadingPulse {
  0% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.02);
  }
  100% {
    opacity: 0.6;
    transform: scale(1);
  }
}

@keyframes loadingSpin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes loadingBounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* Loading skeleton para sugestões */
.suggestionsSkeleton {
  background: #23272f;
  border: 1px solid #333;
  border-radius: 0.5rem;
  padding: 0;
  margin-top: 2.6rem;
  width: 100%;
  max-height: 200px;
  overflow: hidden;
}

.skeletonItem {
  padding: 0.75rem 1rem;
  background: linear-gradient(90deg, #333 25%, #444 50%, #333 75%);
  background-size: 200% 100%;
  animation: skeletonShimmer 1.5s infinite;
  height: 20px;
  margin-bottom: 1px;
}

@keyframes skeletonShimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.nextClipButton {
  background: #1DB954;
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 2rem;
  font-size: 1.1rem;
  cursor: pointer;
  margin-top: 1rem;
  transition: all 0.3s ease;
}

.nextClipButton:hover {
  background: #1ed760;
  transform: scale(1.05);
}

.hintBox {
  background: rgba(255,255,255,0.08);
  border-left: 4px solid #1DB954;
  padding: 1rem;
  margin-top: 1rem;
  border-radius: 0.5rem;
  color: #fff;
  font-size: 1.1rem;
  max-width: 500px;
  text-align: left;
}

.darkBg {
  min-height: 100vh;
  width: 100%;
  color: #fff;
  font-family: 'Segoe UI', Arial, sans-serif;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  box-sizing: border-box;
}

.topBar {
  width: 100%;
  padding: 1rem 0.5rem 0.5rem 0.5rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  background: transparent;
  position: relative;
  z-index: 2;
  min-height: 120px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.2);
}

.titleBar {
  font-size: 1.3rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
  color: #fff;
  letter-spacing: 0.5px;
}

.songInfoBar {
  display: flex;
  gap: 2.5rem;
  background: #181c22;
  border-radius: 0.5rem;
  padding: 0.5rem 2rem;
  margin-bottom: 0.5rem;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.infoLabel {
  font-size: 0.85rem;
  color: #aaa;
}

.infoValue {
  font-size: 1.1rem;
  font-weight: bold;
  color: #fff;
}

.gameAreaModern {
  width: 100%;
  max-width: 520px;
  margin: 2rem auto 0 auto;
  background: #23272f;
  border-radius: 1rem;
  padding: 2rem 1.5rem 1.5rem 1.5rem;
  box-shadow: 0 4px 24px rgba(0,0,0,0.25);
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.clipList {
  width: 100%;
  margin: 0 0 1.5rem 0;
  padding: 0;
  list-style: none;
}

.clipItem, .clipItemActive, .clipItemHint {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: #23272f;
  border-radius: 1.5rem;
  margin-bottom: 0.7rem;
  padding: 0.7rem 1.2rem;
  font-size: 1.1rem;
  color: #bbb;
  border: 2px solid transparent;
  transition: background 0.2s, color 0.2s, border 0.2s;
}

.clipItemActive {
  background: #23272f;
  color: #1DB954;
  border: 2px solid #1DB954;
  font-weight: bold;
}

.clipNumber {
  font-size: 1.1rem;
  font-weight: bold;
  color: #888;
  min-width: 1.5rem;
  text-align: center;
}

.clipName {
  flex: 1;
  color: #bbb;
}

.clipNameActive {
  flex: 1;
  color: #1DB954;
  font-weight: bold;
}

.clipIcon {
  font-size: 1.3rem;
  margin-left: auto;
}

.clipItemHint {
  background: #23272f;
  color: #fff;
  border: 2px dashed #1DB954;
  font-style: italic;
  opacity: 0.85;
}

.clipNameHint {
  flex: 1;
  color: #fff;
}

.hintButton {
  background: #1DB954;
  color: #fff;
  border: none;
  border-radius: 50%;
  width: 2.2rem;
  height: 2.2rem;
  font-size: 1.2rem;
  cursor: pointer;
  margin-left: 0.5rem;
  transition: background 0.2s;
}

.hintButton:disabled {
  background: #444;
  color: #aaa;
  cursor: not-allowed;
}

.hintBoxModern {
  background: #1a1e24;
  border-left: 4px solid #1DB954;
  padding: 1rem;
  margin: 1rem 0 1.5rem 0;
  border-radius: 0.5rem;
  color: #fff;
  font-size: 1.1rem;
  max-width: 500px;
  text-align: left;
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

.guessFormModern {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  position: relative;
  margin-bottom: 1.5rem;
}

/* 📱 RESPONSIVIDADE PARA FORMULÁRIOS */
@media (max-width: 768px) {
  .guessFormModern {
    margin-bottom: 1rem;
  }
}

.guessInputModern {
  padding: 1rem;
  border: 2px solid #1DB954;
  border-radius: 0.7rem;
  background: #181c22;
  color: #fff;
  font-size: 1.1rem;
  margin-bottom: 0.7rem;
  width: 100%;
  transition: border 0.2s;
}

/* 📱 RESPONSIVIDADE PARA INPUTS */
@media (max-width: 768px) {
  .guessInputModern {
    padding: 0.8rem;
    font-size: 1rem;
    margin-bottom: 0.6rem;
  }
}

@media (max-width: 480px) {
  .guessInputModern {
    padding: 0.7rem;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
  }
}

.guessInputModern:focus {
  outline: none;
  border-color: #1ed760;
}

.guessButtonModern {
  background: #1DB954;
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 0.7rem;
  font-size: 1.1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  margin-bottom: 0.5rem;
}

/* 📱 RESPONSIVIDADE PARA BOTÕES */
@media (max-width: 768px) {
  .guessButtonModern {
    padding: 0.8rem 1.5rem;
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .guessButtonModern {
    padding: 0.7rem 1rem;
    font-size: 0.9rem;
  }
}

.guessButtonModern:disabled {
  background: #444;
  color: #aaa;
  cursor: not-allowed;
}

.suggestionsListModern {
  position: absolute;
  background: #23272f;
  border: 1px solid #1DB954;
  border-radius: 0.5rem;
  margin-top: 2.6rem;
  width: 100%;
  max-height: 200px;
  overflow-y: auto;
  z-index: 100;
  list-style: none;
  padding: 0;
  box-shadow: 0 4px 16px rgba(0,0,0,0.3);
}

.suggestionItemModern {
  padding: 0.75rem 1rem;
  cursor: pointer;
  color: #fff;
  transition: background 0.2s;
}

.suggestionItemModern:hover {
  background: #1DB954;
  color: #222;
}

.messageModern {
  font-size: 1.2rem;
  text-align: center;
  padding: 1rem;
  border-radius: 0.5rem;
  background: rgba(255, 255, 255, 0.08);
  width: 100%;
  margin-bottom: 1rem;
}

.newGameButtonModern {
  background: #1DB954;
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 2rem;
  font-size: 1.2rem;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 1rem;
  width: 100%;
}

.newGameButtonModern:hover {
  background: #1ed760;
  transform: scale(1.05);
}

.audioControlsModern {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1.2rem;
  width: 100%;
  margin: 2rem 0 0 0;
  background: transparent;
}

.playButtonModern, .stopButtonModern, .nextClipButtonModern {
  background: #23272f;
  color: #fff;
  border: 2px solid #1DB954;
  border-radius: 50%;
  width: 3.2rem;
  height: 3.2rem;
  font-size: 1.5rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.2s, border 0.2s, color 0.2s;
}

.playButtonModern:hover, .stopButtonModern:hover, .nextClipButtonModern:hover {
  background: #1DB954;
  color: #23272f;
  border-color: #1ed760;
}

.playButtonModern:disabled, .stopButtonModern:disabled, .nextClipButtonModern:disabled {
  background: #444;
  color: #aaa;
  border-color: #444;
  cursor: not-allowed;
}

.nextClipButtonModern {
  border-radius: 1.5rem;
  width: auto;
  padding: 0 1.5rem;
  font-size: 1.1rem;
  height: 3.2rem;
}

.volumeSliderModern {
  width: 120px;
  accent-color: #1DB954;
  margin-left: 1rem;
  background: transparent;
}

.attemptsInfo {
  background: #1a1e24;
  color: #1DB954;
  padding: 1rem;
  border-radius: 0.7rem;
  font-size: 1.2rem;
  font-weight: bold;
  text-align: center;
  margin-bottom: 1.5rem;
  border: 2px solid #1DB954;
  width: 100%;
}

@media (max-width: 600px) {
  .gameAreaModern {
    padding: 1rem 0.2rem;
  }
  .songInfoBar {
    flex-direction: column;
    gap: 0.5rem;
    padding: 0.5rem 0.5rem;
  }
  .audioControlsModern {
    flex-direction: column;
    gap: 0.7rem;
  }

  /* 📱 MELHORAR SUGESTÕES EM MOBILE */
  .suggestionsListModern {
    position: fixed; /* Mudar para fixed para evitar cortes */
    left: 1rem;
    right: 1rem;
    width: auto; /* Auto width para se ajustar às margens */
    max-height: 250px; /* Aumentar altura máxima */
    margin-top: 0.5rem; /* Reduzir margem superior */
    z-index: 1000; /* Aumentar z-index */
    box-shadow: 0 8px 32px rgba(0,0,0,0.5); /* Sombra mais forte */
  }

  .suggestionItemModern {
    padding: 1rem; /* Aumentar padding para touch */
    font-size: 1rem; /* Tamanho de fonte adequado para mobile */
    border-bottom: 1px solid #333; /* Separador visual */
  }

  .suggestionItemModern:last-child {
    border-bottom: none;
  }
}

.audioModernBox {
  width: 100%;
  background: #23273a;
  border-radius: 12px;
  padding: 1.2rem 1.2rem 1.5rem 1.2rem;
  margin-bottom: 1.2rem;
  box-shadow: 0 2px 12px rgba(0,0,0,0.12);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.audioModernBox audio {
  width: 100%;
  background: transparent;
  border-radius: 10px;
  outline: none;
  box-shadow: none;
  margin: 0;
  padding: 0;
  border: none;
}

/* Customização avançada do player nativo */
.audioModernBox audio::-webkit-media-controls-panel {
  background: #23273a !important;
  border-radius: 10px;
}
.audioModernBox audio::-webkit-media-controls-enclosure {
  background: #23273a !important;
  border-radius: 10px;
}
/* Botão play/pause e volume brancos */
.audioModernBox audio::-webkit-media-controls-play-button,
.audioModernBox audio::-webkit-media-controls-mute-button,
.audioModernBox audio::-webkit-media-controls-volume-slider,
.audioModernBox audio::-webkit-media-controls-volume-control-container {
  filter: invert(1) brightness(2) !important;
  background: none !important;
}
/* Remover três pontinhos (menu) */
.audioModernBox audio::-webkit-media-controls-overflow-button {
  display: none !important;
}
/* Tempo sem brilho branco */
.audioModernBox audio::-webkit-media-controls-current-time-display,
.audioModernBox audio::-webkit-media-controls-time-remaining-display {
  color: #fff !important;
  text-shadow: none !important;
  background: none !important;
  font-weight: 400 !important;
}

.audioPlayerModern {
  display: flex;
  align-items: center;
  width: 100%;
  margin-top: 0.5rem;
  background: none;
  padding: 0;
  gap: 0.5rem;
}

.audioTime {
  color: #bbb;
  font-size: 1rem;
  min-width: 44px;
  text-align: right;
  margin-right: 0.7rem;
  flex-shrink: 0;
}

.audioSeekbar {
  flex: 1 1 auto;
  accent-color: #1DB954;
  height: 4px;
  margin: 0;
  min-width: 0;
  max-width: 100%;
}

.audioPlayBtn {
  background: #fff;
  color: #23272f;
  border: none;
  border-radius: 50%;
  width: 2.5rem;
  height: 2.5rem;
  font-size: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0,0,0,0.10);
  cursor: pointer;
  transition: background 0.2s, color 0.2s;
  margin-left: 0.5rem;
  flex-shrink: 0;
}
.audioPlayBtn:disabled {
  background: #eee;
  color: #aaa;
  cursor: not-allowed;
}

.audioVolume {
  width: 80px;
  accent-color: #1DB954;
  background: transparent;
  margin-left: 0.7rem;
  flex-shrink: 0;
}

.attemptsRow {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 1.2rem 0 1.2rem 0;
  justify-content: center;
}

.attemptButton.attemptInactive {
  background-color: #31364d !important;
  color: #bbb;
}
.attemptButton.attemptActive {
  background-color: #1DB954 !important;
  color: #fff;
}
.attemptButton.attemptFail {
  background-color: #e74c3c !important;
  color: #fff;
}
.attemptButton.attemptSuccess {
  background-color: #27ae60 !important;
  color: #fff;
}
.attemptButton.attemptGame {
  background-color: #ffd700 !important;
  color: #222;
}
.attemptButton.attemptFranchise {
  background-color: #ff9800 !important;
  color: #fff;
}

.attemptButton {
  border: none;
  background: none;
  box-shadow: none;
  outline: none;
  padding: 0.4rem 0.9rem;
  margin: 0 4px;
  font: inherit;
  font-size: 1.1rem;
  font-weight: bold;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 2.2rem;
  min-height: 2.2rem;
  cursor: pointer;
  transition: none;
}

.attemptButton:disabled {
  cursor: default;
  opacity: 1;
}

.skipBtn {
  background: #1DB954;
  color: #fff;
  border: none;
  border-radius: 6px;
  font-size: 1.1rem;
  font-weight: bold;
  padding: 0.4rem 1.1rem;
  margin-left: 0.7rem;
  cursor: pointer;
  transition: background 0.2s;
}
.skipBtn:disabled {
  background: #444;
  color: #aaa;
  cursor: not-allowed;
}

.historyBox {
  width: 100%;
  margin: 1.2rem 0 0.5rem 0;
  display: flex;
  flex-direction: column;
  gap: 0.3rem;
}

.historyItem {
  background: #181c22;
  border: 1px solid #23273a;
  border-radius: 6px;
  color: #fff;
  font-size: 1.05rem;
  padding: 0.5rem 1rem;
  display: flex;
  align-items: center;
  gap: 0.7rem;
}

.historySkipped {
  color: #ff6b6b;
  font-weight: bold;
}
.historyFail {
  color: #ff6b6b;
}
.historyFailButCorrectGame {
  color: #ffd700;
}
.historyFailButCorrectFranchise {
  color: #ff9800;
}
.historySuccess {
  color: #1DB954;
  font-weight: bold;
}

.timerBox {
  margin-top: 1.5rem;
  text-align: center;
  color: #bbb;
  font-size: 1.1rem;
}
.timer {
  color: #1DB954;
  font-weight: bold;
  margin-left: 0.3rem;
}

.customAudioPlayer {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  width: 100%;
  background: #23273a;
  border-radius: 10px;
  padding: 1.1rem 1.2rem;
  gap: 0.5rem;
  box-sizing: border-box;
  justify-content: center;
}
.audioPlayerRow {
  display: flex;
  flex-direction: row;
  align-items: center;
  width: 100%;
  justify-content: center;
  gap: 1.1rem;
}
.audioVolumeRow {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  width: 100%;
  margin-top: 0.7rem;
  gap: 0.5rem;
}
.audioSeekbarCustom,
.audioVolumeCustom {
  -webkit-appearance: none;
  appearance: none;
  width: 100%;
  background: transparent;
  height: 4px;
  outline: none;
  margin: 0;
  padding: 0;
}
.audioSeekbarCustom::-webkit-slider-runnable-track,
.audioVolumeCustom::-webkit-slider-runnable-track {
  height: 4px;
  background: #1DB954;
  border-radius: 2px;
}
.audioSeekbarCustom::-webkit-slider-thumb,
.audioVolumeCustom::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #fff;
  border: 2px solid #1DB954;
  box-shadow: 0 2px 8px rgba(29,185,84,0.10);
  cursor: pointer;
  transition: background 0.2s, border 0.2s;
  margin-top: -6px;
}
.audioSeekbarCustom:focus::-webkit-slider-thumb,
.audioVolumeCustom:focus::-webkit-slider-thumb {
  border: 2px solid #1ed760;
}
.audioSeekbarCustom::-ms-fill-lower,
.audioVolumeCustom::-ms-fill-lower {
  background: #1DB954;
}
.audioSeekbarCustom::-ms-fill-upper,
.audioVolumeCustom::-ms-fill-upper {
  background: #1DB954;
}
.audioSeekbarCustom:focus,
.audioVolumeCustom:focus {
  outline: none;
}
/* Firefox */
.audioSeekbarCustom::-moz-range-thumb,
.audioVolumeCustom::-moz-range-thumb {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #fff;
  border: 2px solid #1DB954;
  box-shadow: 0 2px 8px rgba(29,185,84,0.10);
  cursor: pointer;
  transition: background 0.2s, border 0.2s;
}
.audioSeekbarCustom::-moz-range-track,
.audioVolumeCustom::-moz-range-track {
  height: 4px;
  background: #1DB954;
  border-radius: 2px;
}
/* Remove border in Firefox */
.audioSeekbarCustom,
.audioVolumeCustom {
  border: none;
}
/* IE */
.audioSeekbarCustom::-ms-thumb,
.audioVolumeCustom::-ms-thumb {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #fff;
  border: 2px solid #1DB954;
  box-shadow: 0 2px 8px rgba(29,185,84,0.10);
  cursor: pointer;
  transition: background 0.2s, border 0.2s;
}
.audioSeekbarCustom::-ms-fill-lower,
.audioVolumeCustom::-ms-fill-lower {
  background: #1DB954;
}
.audioSeekbarCustom::-ms-fill-upper,
.audioVolumeCustom::-ms-fill-upper {
  background: #1DB954;
}
.audioPlayBtnCustom {
  background: #5a5a5a !important;
  color: #fff !important;
  border: none;
  border-radius: 50%;
  width: 2.5rem;
  height: 2.5rem;
  font-size: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0,0,0,0.10);
  cursor: pointer;
  transition: background 0.2s, color 0.2s;
  margin: 0 0.5rem;
}
.audioPlayBtnCustom:disabled {
  background: #222 !important;
  color: #aaa !important;
  cursor: not-allowed;
}
.audioVolumeCustom {
  width: 70px;
  accent-color: #1DB954;
  background: transparent;
  margin-left: 0.5rem;
  margin-right: 0.2rem;
}
.customAudioPlayer .audioTime {
  color: #eee;
  font-size: 1rem;
  min-width: 44px;
  text-align: right;
  font-variant-numeric: tabular-nums;
  text-shadow: none;
  background: none;
  font-weight: 400;
  margin: 0 0.5rem;
}
.customAudioPlayer .audioTime:last-child {
  margin-left: 0.7rem;
}
.customAudioPlayer svg {
  color: #fff;
  min-width: 20px;
  min-height: 20px;
}
/* 📱 RESPONSIVIDADE COMPLETA PARA MOBILE */
@media (max-width: 768px) {
  .customAudioPlayer {
    padding: 1rem 0.8rem;
    gap: 0.8rem;
  }

  .audioPlayerRow {
    gap: 0.8rem;
  }

  .audioVolumeRow {
    gap: 0.8rem;
  }

  .audioPlayBtnCustom {
    width: 2.2rem;
    height: 2.2rem;
    font-size: 1.3rem;
  }

  .audioTime {
    font-size: 0.9rem;
    min-width: 40px;
  }

  .audioVolumeCustom {
    width: 60px;
  }

  .attemptsRow {
    gap: 0.3rem;
    flex-wrap: wrap;
    justify-content: center;
  }

  .attemptButton {
    min-width: 2rem;
    min-height: 2rem;
    font-size: 1rem;
    padding: 0.3rem 0.7rem;
    margin: 0 2px;
  }

  .skipBtn {
    font-size: 1rem;
    padding: 0.3rem 0.8rem;
  }
}

@media (max-width: 600px) {
  .customAudioPlayer {
    flex-direction: column;
    gap: 0.7rem;
    padding: 0.7rem 0.5rem;
  }
  .audioPlayerRow, .audioVolumeRow {
    flex-direction: column;
    gap: 0.5rem;
    width: 100%;
  }
  .audioSeekbarCustom {
    width: 100%;
    margin: 0.5rem 0;
    max-width: 100%;
  }
  .audioVolumeCustom {
    width: 100px;
  }

  .attemptsRow {
    gap: 0.2rem;
  }

  .attemptButton {
    min-width: 1.8rem;
    min-height: 1.8rem;
    font-size: 0.9rem;
    padding: 0.2rem 0.5rem;
  }
}

@media (max-width: 480px) {
  .audioPlayBtnCustom {
    width: 2rem;
    height: 2rem;
    font-size: 1.2rem;
  }

  .audioTime {
    font-size: 0.8rem;
    min-width: 35px;
  }

  .attemptsRow {
    gap: 0.3rem;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
  }

  .attemptButton {
    min-width: 1.6rem;
    min-height: 1.6rem;
    font-size: 0.85rem;
    padding: 0.2rem 0.4rem;
    margin: 0 1px;
  }

  .skipBtn {
    font-size: 0.9rem;
    padding: 0.25rem 0.6rem;
    margin-left: 0.4rem;
  }
}

/* 📱 RESPONSIVIDADE PARA TELAS MUITO PEQUENAS */
@media (max-width: 360px) {
  .titleBarContainer {
    padding: 0.25rem;
  }

  .titleBarContainer .menuButton {
    left: 0.25rem;
    top: 0.25rem;
  }

  .titleBarContainer .headerButtons {
    right: 0.25rem;
    top: 0.25rem;
    gap: 0.2rem;
  }

  .titleBarContainer img {
    height: 50px;
    width: auto;
    max-width: 120px;
    margin: 0.25rem 0;
  }

  .menuButton, .helpButton {
    padding: 0.25rem;
    font-size: 0.9rem;
  }

  .headerButtons {
    gap: 0.2rem !important;
  }

  .topBar {
    padding: 0.25rem 0.15rem 0.15rem 0.15rem;
    min-height: 80px;
    gap: 0.3rem;
  }

  .attemptButton {
    min-width: 1.4rem;
    min-height: 1.4rem;
    font-size: 0.8rem;
    padding: 0.15rem 0.3rem;
    margin: 0;
  }

  .skipBtn {
    font-size: 0.8rem;
    padding: 0.2rem 0.5rem;
    margin-left: 0.3rem;
  }

  .attemptsRow {
    gap: 0.2rem;
  }

  .customAudioPlayer {
    padding: 0.5rem 0.3rem;
    gap: 0.5rem;
  }

  .audioPlayBtnCustom {
    width: 1.8rem;
    height: 1.8rem;
    font-size: 1rem;
  }

  .audioTime {
    font-size: 0.75rem;
    min-width: 30px;
  }
}

.attemptsRow button {
  border: none;
  background: none;
  box-shadow: none;
  outline: none;
  margin: 0 4px;
  font: inherit;
  cursor: pointer;
  transition: none;
}

.attemptsRow button:disabled {
  cursor: default;
  opacity: 1;
}

.titleBarContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  width: 100%;
  padding: 0 1rem;
  min-height: 80px;
  z-index: 5;
}

.titleBarContainer .menuButton {
  position: absolute;
  left: 1rem;
}

.titleBarContainer .headerButtons {
  position: absolute;
  right: 1rem;
}

.titleBarContainer img{
  height: 100px;
  width: auto;
  max-width: 250px;
}

/* 📱 RESPONSIVIDADE DO HEADER */
@media (max-width: 768px) {
  .titleBarContainer {
    padding: 0.5rem;
    flex-direction: column;
    align-items: center;
    gap: 0.8rem;
    min-height: 120px;
  }

  .titleBarContainer .menuButton {
    position: absolute;
    left: 0.5rem;
    top: 0.5rem;
    order: 1;
    z-index: 10;
  }

  .titleBarContainer .headerButtons {
    position: absolute;
    right: 0.5rem;
    top: 0.5rem;
    order: 3;
    display: flex;
    flex-wrap: wrap;
    gap: 0.3rem;
    z-index: 10;
  }

  .titleBarContainer img {
    height: 60px;
    width: auto;
    max-width: 160px;
    order: 2;
    margin: 0.5rem 0;
  }

  .menuButton, .helpButton {
    padding: 0.4rem;
    font-size: 1rem;
    min-width: 40px;
    min-height: 40px;
  }

  .topBar {
    padding: 0.5rem 0.3rem 0.3rem 0.3rem;
    min-height: 140px;
    gap: 0.8rem;
  }

  .songInfoBar {
    gap: 1.5rem;
    padding: 0.4rem 1rem;
    margin-bottom: 0.3rem;
  }

  .infoLabel {
    font-size: 0.8rem;
  }

  .infoValue {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .titleBarContainer {
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    min-height: 100px;
  }

  .titleBarContainer .menuButton {
    position: absolute;
    left: 0.3rem;
    top: 0.3rem;
    order: 1;
    z-index: 10;
  }

  .titleBarContainer .headerButtons {
    position: absolute;
    right: 0.3rem;
    top: 0.3rem;
    order: 3;
    display: flex;
    flex-direction: column;
    gap: 0.2rem;
    z-index: 10;
  }

  .titleBarContainer img {
    height: 50px;
    width: auto;
    max-width: 140px;
    order: 2;
    margin: 0.3rem 0;
  }

  .menuButton, .helpButton {
    padding: 0.3rem;
    font-size: 0.9rem;
    min-width: 36px;
    min-height: 36px;
  }

  .headerButtons {
    gap: 0.2rem !important;
  }

  .topBar {
    padding: 0.3rem 0.2rem 0.2rem 0.2rem;
    min-height: 120px;
    gap: 0.5rem;
  }

  .songInfoBar {
    flex-direction: column;
    gap: 0.3rem;
    padding: 0.3rem 0.5rem;
    text-align: center;
  }

  .infoLabel {
    font-size: 0.75rem;
  }

  .infoValue {
    font-size: 0.9rem;
  }
}

.headerButtons {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
  z-index: 10;
}

.menuButton, .helpButton {
  background: none;
  border: none;
  color: #fff;
  cursor: pointer;
  padding: 0.5rem;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.menuButton {
  background-color: rgba(29, 185, 84, 0.15);
  border-radius: 50%;
  padding: 0.6rem;
  margin-left: 0.5rem;
}

.menuButton:hover, .helpButton:hover {
  color: #1DB954;
  transform: scale(1.1);
}

.menuButton:hover {
  background-color: rgba(29, 185, 84, 0.25);
}

.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 1rem;
  overflow-y: auto;
  backdrop-filter: blur(5px);
}

.modal {
  background: #23272f;
  border-radius: 1rem;
  padding: 2rem;
  position: relative;
  max-width: 500px;
  width: 100%;
  max-height: calc(100vh - 40px);
  overflow-y: auto;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.3);
  animation: slideInFromTop 0.3s ease-out;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.closeButton {
  position: absolute;
  top: 1.5rem;
  right: 1.5rem;
  background: none;
  border: none;
  color: #fff;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  line-height: 1;
  transition: color 0.2s;
  border-radius: 50%;
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.closeButton:hover {
  color: #1DB954;
}

.modal h2 {
  color: #1DB954;
  margin: 0 0 1.5rem 0;
  font-size: 1.5rem;
}

.modalContent p {
  margin: 0.8rem 0;
  color: #fff;
  font-size: 1.1rem;
  line-height: 1.5;
}

.legendBox {
  background: #181c22;
  border-radius: 0.5rem;
  padding: 1rem;
  margin: 1.5rem 0;
}

.legendItem {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0.5rem 0;
  color: #fff;
}

.legendCorrect {
  color: #1DB954;
}

.legendIncorrect {
  color: #ff6b6b;
}

.legendFromGame {
  color: #ffd700;
}

.goodLuck {
  color: #1DB954 !important;
  font-weight: bold;
  text-align: center;
  margin-top: 1.5rem !important;
}

/* Scrollbar customizada para o modal */
.modal::-webkit-scrollbar {
  width: 8px;
}

.modal::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.modal::-webkit-scrollbar-thumb {
  background: rgba(29, 185, 84, 0.5);
  border-radius: 4px;
}

.modal::-webkit-scrollbar-thumb:hover {
  background: rgba(29, 185, 84, 0.7);
}

/* Scrollbar para Firefox */
.modal {
  scrollbar-width: thin;
  scrollbar-color: rgba(29, 185, 84, 0.5) rgba(255, 255, 255, 0.1);
}

@media (max-width: 600px) {
  .modal {
    margin: 1rem;
    padding: 1.5rem;
  }
}

@keyframes shakeAnimation {
  0%, 100% { transform: translateX(0); }
  20%, 60%, 100% { transform: translateX(-2px); }
  40%, 80% { transform: translateX(2px); }
}

.shake {
  animation: shakeAnimation 0.4s ease-in-out;
}

@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Efeito visual do sacabambapis */
.sacabambapis {
  position: fixed;
  inset: 0; /* Garante que o elemento cubra toda a viewport */
  background: rgba(0, 0, 0, 1); /* Garante que o fundo seja totalmente opaco */
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  animation: sacabambapisAppear 2s ease-out;
}

.sacabambapisImage {
  max-width: 80vw; /* Reverte para o tamanho original */
  max-height: 80vh; /* Reverte para o tamanho original */
  object-fit: contain;
  animation: sacabambapisZoom 2s ease-out;
  filter: drop-shadow(0 0 20px rgba(255, 255, 255, 0.5));
}

@keyframes sacabambapisAppear {
  0% {
    opacity: 0;
    backdrop-filter: blur(0px);
  }
  10% {
    opacity: 1;
    backdrop-filter: blur(10px);
  }
  90% {
    opacity: 1;
    backdrop-filter: blur(10px);
  }
  100% {
    opacity: 0;
    backdrop-filter: blur(0px);
  }
}

@keyframes sacabambapisZoom {
  0% {
    transform: scale(0) rotate(-180deg);
    opacity: 0;
  }
  20% {
    transform: scale(1.2) rotate(0deg);
    opacity: 1;
  }
  80% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
  }
  100% {
    transform: scale(0.8) rotate(0deg);
    opacity: 0;
  }
}

/* Estilos do Modo Infinito */
.modeSelector {
  display: flex;
  gap: 0.5rem;
  margin: 1rem 0;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 0.5rem;
  padding: 0.3rem;
  backdrop-filter: blur(10px);
}

.modeButton {
  padding: 0.7rem 1.5rem;
  border: none;
  border-radius: 0.3rem;
  background: transparent;
  color: #ccc;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.modeButton:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
}

.modeButton:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.modeActive {
  background: #1DB954 !important;
  color: #fff !important;
  font-weight: bold;
}

.infiniteStats {
  display: flex;
  gap: 1.5rem;
  margin: 1rem 0;
  padding: 1rem;
  background: rgba(29, 185, 84, 0.1);
  border-radius: 0.5rem;
  border: 1px solid rgba(29, 185, 84, 0.3);
  flex-wrap: wrap;
  justify-content: center;
}

.infiniteStat {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  min-width: 100px;
}

.infiniteStatLabel {
  font-size: 0.8rem;
  color: #ccc;
  margin-bottom: 0.3rem;
}

.infiniteStatValue {
  font-size: 1.2rem;
  font-weight: bold;
  color: #1DB954;
}

.infiniteGameOverBox {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 1rem;
  padding: 2rem;
  text-align: center;
  margin: 1rem 0;
  backdrop-filter: blur(10px);
  border: 2px solid rgba(29, 185, 84, 0.3);
}

.infiniteGameOverBox h3 {
  color: #1DB954;
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

.newRecord {
  color: #FFD700;
  font-weight: bold;
  font-size: 1.1rem;
  margin: 0.5rem 0;
  animation: pulse 1.5s infinite;
}

.playAgainButton {
  background: #1DB954;
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 0.5rem;
  font-size: 1.1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 1rem;
}

.playAgainButton:hover {
  background: #1ed760;
  transform: scale(1.05);
}

.nextSongContainer {
  display: flex;
  justify-content: center;
  margin: 1.5rem 0;
}

.nextSongButton {
  background: linear-gradient(135deg, #1DB954, #1ed760);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 0.8rem;
  font-size: 1.1rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 15px rgba(29, 185, 84, 0.3);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.nextSongButton:hover {
  background: linear-gradient(135deg, #1ed760, #1DB954);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(29, 185, 84, 0.4);
}

.nextSongButton:active {
  transform: translateY(0);
  box-shadow: 0 2px 10px rgba(29, 185, 84, 0.3);
}

/* Responsividade para modo infinito */
@media (max-width: 768px) {
  .modeSelector {
    margin: 0.5rem 0;
    gap: 0.3rem;
    padding: 0.3rem;
    flex-wrap: wrap;
    justify-content: center;
  }

  .modeButton {
    padding: 0.6rem 1rem;
    font-size: 0.85rem;
    flex: 1;
    min-width: 110px;
    max-width: 180px;
  }

  .infiniteStats {
    gap: 1rem;
    padding: 0.8rem;
    margin: 0.5rem 0;
  }

  .infiniteStat {
    min-width: 80px;
  }

  .infiniteStatLabel {
    font-size: 0.7rem;
  }

  .infiniteStatValue {
    font-size: 1rem;
  }

  .infiniteGameOverBox {
    padding: 1.5rem;
    margin: 0.5rem 0;
  }

  .infiniteGameOverBox h3 {
    font-size: 1.3rem;
  }

  .nextSongContainer {
    margin: 1rem 0;
  }

  .nextSongButton {
    padding: 0.8rem 1.5rem;
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .modeSelector {
    margin: 0.3rem 0;
    gap: 0.3rem;
    padding: 0.2rem;
    flex-direction: column;
    align-items: center;
  }

  .modeButton {
    padding: 0.5rem 0.8rem;
    font-size: 0.75rem;
    text-align: center;
    width: 100%;
    max-width: 200px;
  }
}

@media (max-width: 360px) {
  .modeSelector {
    margin: 0.2rem 0;
  }

  .modeButton {
    padding: 0.4rem 0.6rem;
    font-size: 0.7rem;
  }
}

/* Tutorial/Welcome Modal - UPDATED v2.0 */
.tutorialOverlay {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  background: rgba(0, 0, 0, 0.9) !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important; /* Centralizar verticalmente */
  z-index: 2000 !important;
  padding: 1rem !important;
  backdrop-filter: blur(10px);
  overflow-y: auto !important;
  /* Garantir posicionamento correto */
  transform: none !important;
  margin: 0 !important;
}

.tutorialModal {
  background: linear-gradient(135deg, #23272f 0%, #1a1e26 100%) !important;
  border-radius: 1.5rem !important;
  padding: 2.5rem !important;
  position: relative !important;
  max-width: 700px !important;
  width: 100% !important;
  max-height: calc(100vh - 120px) !important; /* Deixa espaço para margem */
  overflow-y: auto !important;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5) !important;
  border: 1px solid rgba(29, 185, 84, 0.2) !important;
  animation: tutorialSlideIn 0.5s ease-out !important;
  /* Posicionamento centralizado */
  margin: 0 auto !important; /* Sem margem para centralização perfeita */
  transform: translateZ(0) !important; /* Força aceleração de hardware */
  /* Scroll customizado */
  scrollbar-width: thin;
  scrollbar-color: rgba(29, 185, 84, 0.5) rgba(255, 255, 255, 0.1);
}

/* Scrollbar customizada para Webkit */
.tutorialModal::-webkit-scrollbar {
  width: 8px;
}

.tutorialModal::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.tutorialModal::-webkit-scrollbar-thumb {
  background: rgba(29, 185, 84, 0.5);
  border-radius: 4px;
  transition: background 0.3s ease;
}

.tutorialModal::-webkit-scrollbar-thumb:hover {
  background: rgba(29, 185, 84, 0.7);
}





@keyframes tutorialSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.tutorialHeader {
  text-align: center;
  margin-bottom: 2rem;
}

.tutorialTitle {
  color: #1DB954;
  font-size: 2.2rem;
  font-weight: bold;
  margin: 0 0 0.5rem 0;
  text-shadow: 0 2px 10px rgba(29, 185, 84, 0.3);
}

.tutorialSubtitle {
  color: #b3b3b3;
  font-size: 1.1rem;
  margin: 0;
  font-weight: 300;
}

.tutorialContent {
  color: #fff;
  line-height: 1.6;
}

.tutorialSection {
  margin-bottom: 2rem;
}

.tutorialSectionTitle {
  color: #1DB954;
  font-size: 1.3rem;
  font-weight: bold;
  margin: 0 0 1rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.tutorialSectionTitle::before {
  content: '🎮';
  font-size: 1.2rem;
}

.tutorialDescription {
  color: #e0e0e0;
  margin: 0 0 1.5rem 0;
  font-size: 1rem;
}

.tutorialModes {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.tutorialMode {
  background: rgba(29, 185, 84, 0.1);
  border: 1px solid rgba(29, 185, 84, 0.3);
  border-radius: 0.8rem;
  padding: 1.2rem;
  transition: all 0.3s ease;
}

.tutorialMode:hover {
  background: rgba(29, 185, 84, 0.15);
  border-color: rgba(29, 185, 84, 0.5);
  transform: translateY(-2px);
}

.tutorialModeTitle {
  color: #1DB954;
  font-size: 1.1rem;
  font-weight: bold;
  margin: 0 0 0.5rem 0;
}

.tutorialModeDesc {
  color: #c0c0c0;
  margin: 0;
  font-size: 0.95rem;
}

.tutorialSteps {
  list-style: none;
  padding: 0;
  margin: 0;
}

.tutorialStep {
  color: #e0e0e0;
  margin: 0.8rem 0;
  padding-left: 1.5rem;
  position: relative;
  font-size: 0.95rem;
}

.tutorialStep::before {
  content: '▶';
  color: #1DB954;
  position: absolute;
  left: 0;
  top: 0;
  font-size: 0.8rem;
}

.tutorialTips {
  background: rgba(255, 193, 7, 0.1);
  border: 1px solid rgba(255, 193, 7, 0.3);
  border-radius: 0.8rem;
  padding: 1.2rem;
  margin: 1.5rem 0;
}

.tutorialTips .tutorialSectionTitle {
  color: #ffc107;
  margin-bottom: 0.8rem;
}

.tutorialTips .tutorialSectionTitle::before {
  content: '💡';
}

.tutorialTip {
  color: #f0f0f0;
  margin: 0.5rem 0;
  font-size: 0.9rem;
}

.tutorialButtons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
  flex-wrap: wrap;
}

.tutorialButton {
  background: linear-gradient(135deg, #1DB954 0%, #1ed760 100%);
  color: white;
  border: none;
  border-radius: 2rem;
  padding: 0.8rem 2rem;
  font-size: 1rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(29, 185, 84, 0.3);
  min-width: 140px;
}

.tutorialButton:hover {
  background: linear-gradient(135deg, #1ed760 0%, #1DB954 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(29, 185, 84, 0.4);
}

.tutorialButtonSecondary {
  background: transparent;
  color: #1DB954;
  border: 2px solid #1DB954;
  border-radius: 2rem;
  padding: 0.8rem 2rem;
  font-size: 1rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 140px;
}

.tutorialButtonSecondary:hover {
  background: rgba(29, 185, 84, 0.1);
  transform: translateY(-2px);
}

.tutorialCloseButton {
  position: absolute;
  top: 1.5rem;
  right: 1.5rem;
  background: none;
  border: none;
  color: #b3b3b3;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  line-height: 1;
  transition: color 0.2s;
  border-radius: 50%;
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tutorialCloseButton:hover {
  color: #1DB954;
  background: rgba(29, 185, 84, 0.1);
}

/* Tutorial responsivo */
@media (max-width: 768px) {
  .tutorialOverlay {
    padding: 0.5rem;
    align-items: flex-start;
    /* Garantir que o overlay ocupe toda a tela em mobile */
    position: fixed;
    inset: 0;
  }

  .tutorialModal {
    padding: 1.5rem;
    margin: 40px auto 40px auto;
    max-height: calc(100vh - 80px);
    /* Centralização perfeita em mobile */
    width: calc(100% - 1rem);
    max-width: calc(100vw - 1rem);
  }



  .tutorialTitle {
    font-size: 1.8rem;
  }

  .tutorialSubtitle {
    font-size: 1rem;
  }

  .tutorialButtons {
    flex-direction: column;
    align-items: center;
  }

  .tutorialButton,
  .tutorialButtonSecondary {
    width: 100%;
    max-width: 250px;
  }

  .tutorialModes {
    gap: 0.8rem;
  }

  .tutorialMode {
    padding: 1rem;
  }
}

/* Para telas muito pequenas */
@media (max-width: 480px) {
  .tutorialOverlay {
    padding: 0.25rem;
    align-items: flex-start;
  }

  .tutorialModal {
    padding: 1rem;
    margin: 20px auto 20px auto;
    max-height: calc(100vh - 40px);
    width: calc(100% - 0.5rem);
    max-width: calc(100vw - 0.5rem);
    border-radius: 1rem;
  }



  .tutorialTitle {
    font-size: 1.6rem;
  }

  .tutorialSubtitle {
    font-size: 0.9rem;
  }
}

.spinner {
  width: 1.3em;
  height: 1.3em;
  border: 3px solid #1DB95444;
  border-top: 3px solid #1DB954;
  border-radius: 50%;
  animation: spin 0.7s linear infinite;
  display: inline-block;
  vertical-align: middle;
}
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
.audioPlayBtnCustom:focus {
  outline: 2px solid #1DB954;
  outline-offset: 2px;
  box-shadow: 0 0 0 3px #1DB95455;
  transition: box-shadow 0.2s;
}
.audioPlayBtnCustom:active {
  transform: scale(0.96);
  transition: transform 0.1s;
}
